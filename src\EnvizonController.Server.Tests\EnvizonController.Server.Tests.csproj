<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
    <PackageReference Include="FluentAssertions" Version="7.0.0" />
    <PackageReference Include="Bogus" Version="35.6.1" />
    <PackageReference Include="Testcontainers" Version="4.2.0" />
    <PackageReference Include="AutoFixture" Version="4.18.1" />
    <PackageReference Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageReference Include="AutoFixture.AutoMoq" Version="4.18.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EnvizonController.Server\EnvizonController.Server.csproj" />
    <ProjectReference Include="..\EnvizonController.Application\EnvizonController.Application.csproj" />
    <ProjectReference Include="..\EnvizonController.Domain\EnvizonController.Domain.csproj" />
    <ProjectReference Include="..\EnvizonController.Infrastructure\EnvizonController.Infrastructure.csproj" />
    <ProjectReference Include="..\EnvizonController.Shared\EnvizonController.Shared.csproj" />
    <ProjectReference Include="..\EnvizonController.Mqtt.Server\EnvizonController.Mqtt.Server.csproj" />
  </ItemGroup>

</Project> 