using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Results;
using EnvizonController.Shared.DTOs;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Configuration.Models;

namespace EnvizonController.ApiClient.Services.Implementations
{
    /// <summary>
    /// 设备测试API服务实现
    /// </summary>
    public class DeviceTestApiService : IDeviceTestApiService
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;

        public DeviceTestApiService(IHttpClient httpClient)
        {
            _httpClient = httpClient;
            _config = AppConfigurationProvider.ConfigurationAsyncLoaded;
        }

        /// <summary>
        /// API服务基础URL
        /// </summary>
        public string BaseUrl => _config.ApiClientSettings.BaseUrl;

        /// <summary>
        /// 初始化API服务
        /// </summary>
        public Task InitializeAsync()
        {
            // 默认实现不需要特殊初始化
            return Task.CompletedTask;
        }
        /// <summary>
        /// 暂停设备测试
        /// </summary>
        public async Task<Result<DeviceTestPauseResult>> PauseDeviceTestAsync(long deviceId, long? testId = null)
        {
            var queryParams = new Dictionary<string, string>();
            if (testId.HasValue)
            {
                queryParams["testId"] = testId.Value.ToString();
            }

            return await _httpClient.PostAsync<DeviceTestPauseResult, object>($"api/devicetest/{deviceId}/pause", queryParams);
        }

        /// <summary>
        /// 检查API服务状态
        /// </summary>
        public async Task<Result<bool>> CheckHealthAsync()
        {
            var result = await _httpClient.GetAsync<object>("api/health");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 启动设备测试
        /// </summary>
        public async Task<Result<DeviceTestStartResult>> StartDeviceTestAsync(DeviceTestStartRequest request)
        {
            return await _httpClient.PostAsync<DeviceTestStartResult, DeviceTestStartRequest>("api/devicetest/start", request);
        }

        /// <summary>
        /// 停止设备测试
        /// </summary>
        public async Task<Result<DeviceTestStopResult>> StopDeviceTestAsync(long deviceId, long? testId = null)
        {
            var queryParams = new Dictionary<string, string>();
            if (testId.HasValue)
            {
                queryParams["testId"] = testId.Value.ToString();
            }

            return await _httpClient.PostAsync<DeviceTestStopResult, object>($"api/devicetest/{deviceId}/stop", queryParams);
        }

        /// <summary>
        /// 获取设备测试状态
        /// </summary>
        public async Task<Result<DeviceTestStatus>> GetDeviceTestStatusAsync(long deviceId)
        {
            return await _httpClient.GetAsync<DeviceTestStatus>($"api/devicetest/{deviceId}/status");
        }

        /// <summary>
        /// 批量启动多个设备测试
        /// </summary>
        public async Task<Result<BatchDeviceTestStartResult>> BatchStartDeviceTestsAsync(BatchDeviceTestStartRequest requests)
        {
            return await _httpClient.PostAsync<BatchDeviceTestStartResult, BatchDeviceTestStartRequest>("api/devicetest/batch-start", requests);
        }

        /// <summary>
        /// 获取多个设备的测试状态
        /// </summary>
        public async Task<Result<List<DeviceTestStatus>>> GetMultipleDeviceTestStatusAsync(string deviceIds)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["deviceIds"] = deviceIds
            };

            return await _httpClient.GetAsync<List<DeviceTestStatus>>("api/devicetest/status", queryParams);
        }
    }
} 