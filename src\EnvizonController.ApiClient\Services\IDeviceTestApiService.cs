using EnvizonController.ApiClient.Results;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 设备测试API服务接口
    /// </summary>
    public interface IDeviceTestApiService : IApiService
    {
        /// <summary>
        /// 暂停设备测试
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="testId">测试ID（可选）</param>
        /// <returns>测试暂停结果</returns>
        Task<Result<DeviceTestPauseResult>> PauseDeviceTestAsync(long deviceId, long? testId = null);

        /// <summary>
        /// 启动设备测试
        /// </summary>
        /// <param name="request">测试启动请求</param>
        /// <returns>测试启动结果</returns>
        Task<Result<DeviceTestStartResult>> StartDeviceTestAsync(DeviceTestStartRequest request);

        /// <summary>
        /// 停止设备测试
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="testId">测试ID（可选）</param>
        /// <returns>测试停止结果</returns>
        Task<Result<DeviceTestStopResult>> StopDeviceTestAsync(long deviceId, long? testId = null);

        /// <summary>
        /// 获取设备测试状态
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>设备测试状态</returns>
        Task<Result<DeviceTestStatus>> GetDeviceTestStatusAsync(long deviceId);

        /// <summary>
        /// 批量启动多个设备测试
        /// </summary>
        /// <param name="requests">批量测试启动请求</param>
        /// <returns>批量启动结果</returns>
        Task<Result<BatchDeviceTestStartResult>> BatchStartDeviceTestsAsync(BatchDeviceTestStartRequest requests);

        /// <summary>
        /// 获取多个设备的测试状态
        /// </summary>
        /// <param name="deviceIds">设备ID列表（逗号分隔）</param>
        /// <returns>多设备测试状态列表</returns>
        Task<Result<List<DeviceTestStatus>>> GetMultipleDeviceTestStatusAsync(string deviceIds);
    }
} 