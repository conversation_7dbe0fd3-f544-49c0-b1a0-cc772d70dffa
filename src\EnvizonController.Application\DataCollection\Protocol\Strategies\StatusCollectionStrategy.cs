﻿using EnvizonController.Application.Devices;
using EnvizonController.Domain.Aggregates;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.Application.DataCollection.Protocol;
using static EnvizonController.Application.DataCollection.Protocol.ProtocolDataCollector;
using EnvizonController.Modbus.Client.Extensions;
using System.Globalization;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Application.DataCollection.Strategies
{
    public class StatusCollectionStrategy : ICollectionStrategy
    {
        private readonly ILogger _logger;

        public StatusCollectionStrategy(ILogger logger)
        {
            _logger = logger.ForContext<StatusCollectionStrategy>();
        }

        public bool CanHandle(ProtocolItem item)
        {
            return item.GroupName == "运行设置";
        }

        public async Task<Dictionary<string, object>> CollectAsync(DeviceCollectionDetails deviceCollectionDetails,
            ProtocolItem item, Device device, IModbusDeviceService modbusDeviceService)
        {
            _logger.Debug(
                "Collecting STATUS data for item: {ItemName} from device: {DeviceName} using address {Address}",
                item.Name, device.Name, item.Address);

            // Simulate actual data collection for a status item
            double rawValue;

            switch (item.DataType)
            {
                case DataType.Float:
                    rawValue = await modbusDeviceService.ModbusClient.ReadFloatAsync(device.SlaveId, item.Address,
                        EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                    break;
                case DataType.Double:
                    rawValue = await modbusDeviceService.ModbusClient.ReadDoubleAsync(device.SlaveId, item.Address,
                        EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                    break;
                case DataType.Int16:
                    rawValue = await modbusDeviceService.ModbusClient.ReadInt16Async(device.SlaveId, item.Address,
                        CancellationToken.None);
                    break;
                case DataType.UInt16:
                    rawValue = await modbusDeviceService.ModbusClient.ReadUInt16Async(device.SlaveId, item.Address,
                        CancellationToken.None);
                    break;
                case DataType.Int32:
                    rawValue = await modbusDeviceService.ModbusClient.ReadInt32Async(device.SlaveId, item.Address,
                        EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                    break;
                case DataType.UInt32:
                    rawValue = await modbusDeviceService.ModbusClient.ReadUInt32Async(device.SlaveId, item.Address,
                        EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                    break;
                case DataType.Bool:
                    var coils = await modbusDeviceService.ModbusClient.ReadCoilsAsync(device.SlaveId, item.Address, 1,
                        CancellationToken.None);
                    rawValue = coils[0] ? 1.0 : 0.0;
                    break;
                default:
                    _logger.Warning("不支持的数据类型: {DataType}，使用默认Float类型", item.DataType);
                    rawValue = await modbusDeviceService.ModbusClient.ReadFloatAsync(device.SlaveId, item.Address,
                        EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                    break;
            }

            var scaledValue = rawValue * (item.ScaleFactor != 0 ? item.ScaleFactor : 1.0) + item.Offset;

            // 分析设备状态信息
            AnalyzeDeviceStatus(deviceCollectionDetails, item, scaledValue, rawValue);

            var collectedData = new Dictionary<string, object>
            {
                { "value", scaledValue },
                { "unit", item.Unit ?? "N/A" },
                { "name", item.DisplayName },
                { "description", $"Status data for {item.Name}" }
            };

            deviceCollectionDetails.DeviceStateData[item.Name] = collectedData;
            return collectedData;
        }

        /// <summary>
        /// 根据协议项目分析设备状态
        /// </summary>
        /// <param name="deviceCollectionDetails">设备采集详情</param>
        /// <param name="item">协议项目</param>
        /// <param name="scaledValue">缩放后的值</param>
        /// <param name="rawValue">原始值</param>
        private void AnalyzeDeviceStatus(DeviceCollectionDetails deviceCollectionDetails, ProtocolItem item, double scaledValue, double rawValue)
        {
            switch (item.Name)
            {
                case "运行状态":
                    // 根据协议定义：0:停止, 1:运行, 2:暂停
                    var statusValue = (int)rawValue;
                    deviceCollectionDetails.OperatingStatus = statusValue switch
                    {
                        0 => Shared.Enums.DeviceOperatingStatus.Stopped,
                        1 => Shared.Enums.DeviceOperatingStatus.Running,
                        2 => Shared.Enums.DeviceOperatingStatus.Paused,
                        _ => Shared.Enums.DeviceOperatingStatus.Unknown
                    };
                    
                    _logger.Debug("设备 {DeviceName} 运行状态: {Status} (原始值: {RawValue})", 
                        item.Name, deviceCollectionDetails.OperatingStatus, rawValue);
                    break;

                case "当前程式步骤":
                    deviceCollectionDetails.CurrentProgramStep = (int)rawValue;
                    _logger.Debug("设备 {DeviceName} 当前程式步骤: {Step}", 
                        item.Name, deviceCollectionDetails.CurrentProgramStep);
                    break;

                case "当前程式步骤是否完成":
                    // 假设非0值表示完成
                    deviceCollectionDetails.IsCurrentProgramStepCompleted = rawValue != 0;
                    _logger.Debug("设备 {DeviceName} 当前程式步骤是否完成: {IsCompleted} (原始值: {RawValue})", 
                        item.Name, deviceCollectionDetails.IsCurrentProgramStepCompleted, rawValue);
                    break;

                default:
                    // 其他状态项目，仅记录数据，不更新设备状态
                    _logger.Debug("收集到状态数据项目: {ItemName} = {Value}", item.Name, scaledValue);
                    break;
            }
        }
    }
}