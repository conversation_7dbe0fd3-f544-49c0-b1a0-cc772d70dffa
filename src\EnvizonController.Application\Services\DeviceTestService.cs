using EnvizonController.Application.DataCollection;
using EnvizonController.Application.DeviceCommands.Interfaces;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Domain.Services;
using EnvizonController.Shared.DTOs;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace EnvizonController.Application.Services;

/// <summary>
/// 设备测试服务 - 负责管理设备测试的完整生命周期
/// 使用策略模式、工厂模式和命令模式等设计模式
/// </summary>
public class DeviceTestService : IDeviceTestService
{
    private readonly IDeviceAppService _deviceService;
    private readonly ITestItemAppService _testItemService;
    private readonly IDataCollectionCoordinator _dataCollectionCoordinator;
    private readonly ITestRunDomainService _testRunDomainService;
    private readonly ILogger<DeviceTestService> _logger;
    private readonly ITestExecutionStrategyFactory _strategyFactory;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IDeviceCommandService _commandService;

    public DeviceTestService(
        IDeviceAppService deviceService,
        ITestItemAppService testItemService, IDeviceCommandService commandService,
        IDataCollectionCoordinator dataCollectionCoordinator,
        ITestRunDomainService testRunDomainService,
        ITestExecutionStrategyFactory strategyFactory,
        IUnitOfWork unitOfWork,
        ILogger<DeviceTestService> logger)
    {
        _commandService = commandService ?? throw new ArgumentNullException(nameof(commandService));
        _deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
        _testItemService = testItemService ?? throw new ArgumentNullException(nameof(testItemService));
        _dataCollectionCoordinator = dataCollectionCoordinator ?? throw new ArgumentNullException(nameof(dataCollectionCoordinator));
        _testRunDomainService = testRunDomainService ?? throw new ArgumentNullException(nameof(testRunDomainService));
        _strategyFactory = strategyFactory ?? throw new ArgumentNullException(nameof(strategyFactory));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 启动设备测试 - 主要入口方法
    /// </summary>
    /// <param name="request">设备测试启动请求</param>
    /// <returns>测试启动结果</returns>
    public async Task<DeviceTestStartResult> StartDeviceTestAsync(DeviceTestStartRequest request)
    {
        _logger.LogInformation("开始启动设备测试，设备ID: {DeviceId}, 测试名称: {TestName}", 
            request.DeviceId, request.TestName);

        try
        {
            // 1. 验证请求参数
            await ValidateStartRequestAsync(request);

            // 2. 获取设备信息
            var device = await GetAndValidateDeviceAsync(request.DeviceId);

            // 3. 使用工厂模式创建测试执行策略
            var strategy = _strategyFactory.CreateStrategy(request.ExecutionType);

            // 4. 创建测试运行
            var testRun = await strategy.CreateTestRunAsync(request, device);

            // 5. 启动测试
            var startedTest = await StartTestExecutionAsync(testRun);

            // 6. 启动数据采集
            var collectionResult = await StartDataCollectionAsync(device);

            _logger.LogInformation("设备测试启动成功，测试ID: {TestId}, 设备ID: {DeviceId}", 
                startedTest.Id, device.Id);

            return new DeviceTestStartResult
            {
                IsSuccess = true,
                TestRun = startedTest,
                Message = "测试启动成功",
                CollectionStarted = collectionResult
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动设备测试失败，设备ID: {DeviceId}", request.DeviceId);
            return new DeviceTestStartResult
            {
                IsSuccess = false,
                Message = $"测试启动失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 停止设备测试
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="testId">测试ID（可选）</param>
    /// <returns>停止结果</returns>
    public async Task<DeviceTestStopResult> StopDeviceTestAsync(long deviceId, long? testId = null)
    {
        _logger.LogInformation("开始停止设备测试，设备ID: {DeviceId}, 测试ID: {TestId}", deviceId, testId);

        try
        {
            // 1. 停止数据采集
            var collectionStopped = await _dataCollectionCoordinator.StopTestCollectionAsync(deviceId);

            // 2. 停止测试运行
            TestRunDTO? stoppedTest = null;
            if (testId.HasValue)
            {
                stoppedTest = await _testItemService.StopTestAsync(testId.Value);
            }
            else
            {
                // 查找设备当前运行的测试
                var runningTest = await FindRunningTestForDeviceAsync(deviceId);
                if (runningTest != null)
                {
                    stoppedTest = await _testItemService.StopTestAsync(runningTest.Id);
                }
            }

            _logger.LogInformation("设备测试停止完成，设备ID: {DeviceId}", deviceId);

            return new DeviceTestStopResult
            {
                IsSuccess = true,
                TestRun = stoppedTest,
                Message = "测试停止成功",
                CollectionStopped = collectionStopped
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止设备测试失败，设备ID: {DeviceId}", deviceId);
            return new DeviceTestStopResult
            {
                IsSuccess = false,
                Message = $"测试停止失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 获取设备测试状态
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备测试状态</returns>
    public async Task<DeviceTestStatus> GetDeviceTestStatusAsync(long deviceId)
    {
        try
        {
            var device = await _deviceService.GetDeviceByIdAsync(deviceId);
            if (device == null)
            {
                return new DeviceTestStatus
                {
                    DeviceId = deviceId,
                    IsDeviceConnected = false,
                    TestStatus = "DeviceNotFound",
                    Message = "设备不存在"
                };
            }

            var runningTest = await FindRunningTestForDeviceAsync(deviceId);
            var isCollecting = _dataCollectionCoordinator.ActiveCollectingDevices.ContainsKey(deviceId);

            return new DeviceTestStatus
            {
                DeviceId = deviceId,
                DeviceName = device.Name,
                IsDeviceConnected = device.ConnectionStatus == Domain.Enums.ConnectionStatus.Connected,
                TestStatus = runningTest?.Status ?? "NoActiveTest",
                CurrentTest = runningTest,
                IsDataCollecting = isCollecting,
                Message = runningTest != null ? "测试运行中" : "无活动测试"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备测试状态失败，设备ID: {DeviceId}", deviceId);
            return new DeviceTestStatus
            {
                DeviceId = deviceId,
                TestStatus = "Error",
                Message = $"获取状态失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 暂停设备测试
    /// </summary>
    public async Task<DeviceTestPauseResult> PauseDeviceTestAsync(long deviceId, long? testId = null)
    {
        try
        {
            // 使用智能指令服务发送暂停指令
            var result = await _commandService.PauseDeviceAsync(deviceId.ToString());

            if (result.IsSuccess)
            {
                // 更新数据库状态
                await UpdateDeviceTestStatusAsync(deviceId, "Paused");

                return new DeviceTestPauseResult
                {
                    IsSuccess = true,
                    Message = "设备测试已成功暂停"
                };
            }
            else
            {
                return new DeviceTestPauseResult
                {
                    IsSuccess = false,
                    Message = $"暂停设备失败: {result.Message}"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "暂停设备测试时发生异常，设备ID: {DeviceId}", deviceId);
            return new DeviceTestPauseResult
            {
                IsSuccess = false,
                Message = $"暂停设备时发生异常: {ex.Message}"
            };
        }
    }
    /// <summary>
    /// 更新设备和测试的状态
    /// </summary>
    private async Task UpdateDeviceTestStatusAsync(long deviceId, string status)
    {
        // 更新设备状态
        await _deviceService.PauseDeviceAsync(deviceId);

        //// 更新测试状态（如有testId可用，也可按需扩展）
        //var runningTest = await FindRunningTestForDeviceAsync(deviceId);
        //if (runningTest != null)
        //{
        //    runningTest.Status = status;
        //    // 如果有TestItemAppService的更新方法，可调用
        //    await _testItemService.UpdateTestStatusAsync(runningTest.Id, status);
        //}
    }

    /// <summary>
    /// 恢复设备测试
    /// </summary>
    public async Task<DeviceTestResumeResult> ResumeDeviceTestAsync(long deviceId, long? testId = null)
    {
        _logger.LogInformation("开始恢复设备测试，设备ID: {DeviceId}, 测试ID: {TestId}", deviceId, testId);

        try
        {
            // 1. 查找要恢复的测试
            TestRunDTO? testToResume = null;
            if (testId.HasValue)
            {
                testToResume = await _testItemService.GetTestRunByIdAsync(testId.Value);
            }
            else
            {
                // 查找设备当前暂停的测试
                var queryParams = new TestItemQueryParams
                {
                    DeviceId = deviceId,
                    Status = "Paused",
                    Page = 1,
                    PageSize = 1
                };
                var result = await _testItemService.GetTestItemsAsync(queryParams);
                testToResume = result.Items?.FirstOrDefault();

                var device = await _deviceService.ContinueDeviceAsync(deviceId);
            }


            if (testToResume == null)
            {
                throw new InvalidOperationException("未找到要恢复的测试");
            }

            // 2. 恢复测试
            var resumedTest = testToResume;

            _logger.LogInformation("设备测试恢复成功，测试ID: {TestId}", testToResume.Id);

            return new DeviceTestResumeResult
            {
                IsSuccess = true,
                TestRun = resumedTest,
                Message = "测试已恢复"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "恢复设备测试失败，设备ID: {DeviceId}", deviceId);
            return new DeviceTestResumeResult
            {
                IsSuccess = false,
                Message = $"测试恢复失败: {ex.Message}"
            };
        }
    }

    #region 私有辅助方法

    /// <summary>
    /// 验证启动请求参数
    /// </summary>
    private async Task ValidateStartRequestAsync(DeviceTestStartRequest request)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        if (request.DeviceId <= 0)
            throw new ArgumentException("设备ID必须大于0", nameof(request.DeviceId));

        if (string.IsNullOrWhiteSpace(request.TestName))
            throw new ArgumentException("测试名称不能为空", nameof(request.TestName));

        // 可以添加更多验证逻辑
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取并验证设备
    /// </summary>
    private async Task<Device> GetAndValidateDeviceAsync(long deviceId)
    {
        var device = await _deviceService.GetDeviceByIdAsync(deviceId);
        if (device == null)
        {
            throw new InvalidOperationException($"设备不存在，ID: {deviceId}");
        }

        if (device.ConnectionStatus != Domain.Enums.ConnectionStatus.Connected)
        {
            throw new InvalidOperationException($"设备未连接，无法启动测试，设备ID: {deviceId}");
        }

        return device;
    }

    /// <summary>
    /// 启动测试执行
    /// </summary>
    private async Task<TestRunDTO> StartTestExecutionAsync(TestRun testRun)
    {
        // 保存测试运行到数据库
        await _unitOfWork.TestItems.AddAsync(testRun);
        await _unitOfWork.SaveChangesAsync();

        // 启动测试
        var testRunDto = await _testItemService.StartTestAsync(testRun.Id);
        return testRunDto ?? throw new InvalidOperationException("启动测试失败");
    }

    /// <summary>
    /// 启动数据采集
    /// </summary>
    private async Task<bool> StartDataCollectionAsync(Device device)
    {
        return await _dataCollectionCoordinator.StartTestCollectionAsync(device);
    }

    /// <summary>
    /// 查找设备当前运行的测试
    /// </summary>
    private async Task<TestRunDTO?> FindRunningTestForDeviceAsync(long deviceId)
    {
        var queryParams = new TestItemQueryParams
        {
            DeviceId = deviceId,
            Status = "Running",
            Page = 1,
            PageSize = 1
        };

        var result = await _testItemService.GetTestItemsAsync(queryParams);
        return result.Items?.FirstOrDefault();
    }

    #endregion
}

/// <summary>
/// 测试执行策略工厂接口
/// </summary>
public interface ITestExecutionStrategyFactory
{
    /// <summary>
    /// 创建测试执行策略
    /// </summary>
    /// <param name="executionType">执行类型</param>
    /// <returns>测试执行策略</returns>
    ITestExecutionStrategy CreateStrategy(string executionType);
}

/// <summary>
/// 测试执行策略接口
/// </summary>
public interface ITestExecutionStrategy
{
    /// <summary>
    /// 创建测试运行
    /// </summary>
    /// <param name="request">启动请求</param>
    /// <param name="device">设备</param>
    /// <returns>测试运行</returns>
    Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device);
}

/// <summary>
/// 设备测试服务接口
/// </summary>
public interface IDeviceTestService
{
    /// <summary>
    /// 启动设备测试
    /// </summary>
    Task<DeviceTestStartResult> StartDeviceTestAsync(DeviceTestStartRequest request);

    /// <summary>
    /// 停止设备测试
    /// </summary>
    Task<DeviceTestStopResult> StopDeviceTestAsync(long deviceId, long? testId = null);

    /// <summary>
    /// 获取设备测试状态
    /// </summary>
    Task<DeviceTestStatus> GetDeviceTestStatusAsync(long deviceId);

    /// <summary>
    /// 暂停设备测试
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="testId">测试ID（可选）</param>
    /// <returns>暂停结果</returns>
    Task<DeviceTestPauseResult> PauseDeviceTestAsync(long deviceId, long? testId = null);

    /// <summary>
    /// 恢复设备测试
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="testId">测试ID（可选）</param>
    /// <returns>恢复结果</returns>
    Task<DeviceTestResumeResult> ResumeDeviceTestAsync(long deviceId, long? testId = null);
}