using EnvizonController.Application.DeviceCommands.Interfaces;
using EnvizonController.Application.DeviceCommands.Models;
using EnvizonController.Application.DeviceCommands.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;

namespace EnvizonController.Application.DeviceCommands.Services
{
    /// <summary>
    /// 智能设备指令服务实现
    /// </summary>
    public class DeviceCommandService : IDeviceCommandService
    {
        private readonly IProtocolCommandResolver _protocolResolver;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IDeviceConnectionManager _connectionManager;
        private readonly CommandMappingConfig _config;
        private readonly ILogger<DeviceCommandService> _logger;

        public DeviceCommandService(
            IProtocolCommandResolver protocolResolver,
            ICommandExecutor commandExecutor,
            IDeviceConnectionManager connectionManager,
            IOptions<CommandMappingConfig> config,
            ILogger<DeviceCommandService> logger)
        {
            _protocolResolver = protocolResolver;
            _commandExecutor = commandExecutor;
            _connectionManager = connectionManager;
            _config = config.Value;
            _logger = logger;
        }

        /// <summary>
        /// 基于指令名称智能发送
        /// </summary>
        public async Task<CommandResult> SendCommandAsync(string deviceId, string commandName, 
            object? parameters = null, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CommandResult();

            try
            {
                if (!long.TryParse(deviceId, out long deviceIdLong))
                {
                    result.IsSuccess = false;
                    result.Message = "无效的设备ID";
                    result.ErrorCode = "INVALID_DEVICE_ID";
                    return result;
                }

                _logger.LogInformation("发送智能指令，设备ID: {DeviceId}, 指令: {CommandName}", deviceId, commandName);

                // 1. 解析指令到协议项
                var protocolItem = await _protocolResolver.ResolveCommandToProtocolItemAsync(deviceIdLong, commandName);
                if (protocolItem == null)
                {
                    result.IsSuccess = false;
                    result.Message = $"未找到指令 '{commandName}' 对应的协议项";
                    result.ErrorCode = "COMMAND_NOT_FOUND";
                    
                    // 提供推荐
                    var recommendations = await _protocolResolver.GetRecommendedProtocolItemsAsync(deviceIdLong, commandName);
                    if (recommendations.Any())
                    {
                        result.Metadata.Add("推荐项", recommendations.Select(r => r.DisplayName).ToList());
                    }
                    
                    return result;
                }

                // 2. 验证参数
                var validation = await _protocolResolver.ValidateCommandParametersAsync(protocolItem, parameters);
                if (!validation.IsValid)
                {
                    result.IsSuccess = false;
                    result.Message = $"参数验证失败: {string.Join(", ", validation.ErrorMessages)}";
                    result.ErrorCode = "PARAMETER_VALIDATION_FAILED";
                    return result;
                }

                // 3. 确保设备连接
                bool isConnected = await _connectionManager.EnsureConnectionAsync(deviceIdLong, cancellationToken);
                if (!isConnected)
                {
                    result.IsSuccess = false;
                    result.Message = "设备连接失败";
                    result.ErrorCode = "CONNECTION_FAILED";
                    return result;
                }

                // 4. 获取设备服务并执行指令
                var deviceService = await _connectionManager.GetDeviceServiceAsync(deviceIdLong, cancellationToken);
                
                if (parameters != null && protocolItem.IsWritable)
                {
                    // 执行写入指令
                    result = await _commandExecutor.ExecuteWriteCommandAsync(deviceService.ModbusClient, protocolItem, parameters, cancellationToken);
                }
                else
                {
                    // 执行读取指令 - 返回object类型的结果
                    var readResult = await _commandExecutor.ExecuteReadCommandAsync<object>(deviceService.ModbusClient, protocolItem, cancellationToken);
                    result.IsSuccess = readResult.IsSuccess;
                    result.Message = readResult.Message;
                    result.ErrorCode = readResult.ErrorCode;
                    result.ExecutionDuration = readResult.ExecutionDuration;
                    result.ExecutedAt = readResult.ExecutedAt;
                    result.Metadata = readResult.Metadata;
                    
                    if (readResult.Data != null)
                    {
                        result.Metadata.Add("读取值", readResult.Data);
                    }
                }

                if (result.IsSuccess)
                {
                    _logger.LogInformation("指令执行成功，设备ID: {DeviceId}, 指令: {CommandName}", deviceId, commandName);
                }
                else
                {
                    _logger.LogWarning("指令执行失败，设备ID: {DeviceId}, 指令: {CommandName}, 错误: {Message}", 
                        deviceId, commandName, result.Message);
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"指令执行异常: {ex.Message}";
                result.ErrorCode = "EXECUTION_EXCEPTION";
                _logger.LogError(ex, "指令执行异常，设备ID: {DeviceId}, 指令: {CommandName}", deviceId, commandName);
            }
            finally
            {
                stopwatch.Stop();
                if (result.ExecutionDuration == TimeSpan.Zero)
                {
                    result.ExecutionDuration = stopwatch.Elapsed;
                }
                result.ExecutedAt = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// 基于协议项名称发送
        /// </summary>
        public async Task<CommandResult<T>> SendByProtocolItemAsync<T>(string deviceId, string protocolItemName, 
            T? value = default, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CommandResult<T>();

            try
            {
                if (!long.TryParse(deviceId, out long deviceIdLong))
                {
                    result.IsSuccess = false;
                    result.Message = "无效的设备ID";
                    result.ErrorCode = "INVALID_DEVICE_ID";
                    return result;
                }

                _logger.LogInformation("基于协议项发送指令，设备ID: {DeviceId}, 协议项: {ProtocolItemName}", 
                    deviceId, protocolItemName);

                // 1. 查找协议项
                var protocolItem = await _protocolResolver.FindProtocolItemByNameAsync(deviceIdLong, protocolItemName);
                if (protocolItem == null)
                {
                    result.IsSuccess = false;
                    result.Message = $"未找到协议项 '{protocolItemName}'";
                    result.ErrorCode = "PROTOCOL_ITEM_NOT_FOUND";
                    return result;
                }

                // 2. 确保设备连接
                bool isConnected = await _connectionManager.EnsureConnectionAsync(deviceIdLong, cancellationToken);
                if (!isConnected)
                {
                    result.IsSuccess = false;
                    result.Message = "设备连接失败";
                    result.ErrorCode = "CONNECTION_FAILED";
                    return result;
                }

                // 3. 获取设备服务并执行指令
                var deviceService = await _connectionManager.GetDeviceServiceAsync(deviceIdLong, cancellationToken);
                
                if (value != null && protocolItem.IsWritable)
                {
                    // 执行写入指令
                    var writeResult = await _commandExecutor.ExecuteWriteCommandAsync(deviceService.ModbusClient, protocolItem, value, cancellationToken);
                    result.IsSuccess = writeResult.IsSuccess;
                    result.Message = writeResult.Message;
                    result.ErrorCode = writeResult.ErrorCode;
                    result.ExecutionDuration = writeResult.ExecutionDuration;
                    result.ExecutedAt = writeResult.ExecutedAt;
                    result.Metadata = writeResult.Metadata;
                    result.Data = value; // 返回写入的值
                }
                else
                {
                    // 执行读取指令
                    result = await _commandExecutor.ExecuteReadCommandAsync<T>(deviceService.ModbusClient, protocolItem, cancellationToken);
                }

                if (result.IsSuccess)
                {
                    _logger.LogInformation("协议项指令执行成功，设备ID: {DeviceId}, 协议项: {ProtocolItemName}", 
                        deviceId, protocolItemName);
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"指令执行异常: {ex.Message}";
                result.ErrorCode = "EXECUTION_EXCEPTION";
                _logger.LogError(ex, "协议项指令执行异常，设备ID: {DeviceId}, 协议项: {ProtocolItemName}", 
                    deviceId, protocolItemName);
            }
            finally
            {
                stopwatch.Stop();
                if (result.ExecutionDuration == TimeSpan.Zero)
                {
                    result.ExecutionDuration = stopwatch.Elapsed;
                }
                result.ExecutedAt = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// 预定义设备控制指令 - 暂停设备
        /// </summary>
        public async Task<CommandResult> PauseDeviceAsync(string deviceId, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("暂停设备，设备ID: {DeviceId}", deviceId);
            return await SendCommandAsync(deviceId, "pause", true, cancellationToken);
        }

        /// <summary>
        /// 预定义设备控制指令 - 恢复设备
        /// </summary>
        public async Task<CommandResult> ResumeDeviceAsync(string deviceId, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("恢复设备，设备ID: {DeviceId}", deviceId);
            return await SendCommandAsync(deviceId, "resume", true, cancellationToken);
        }

        /// <summary>
        /// 预定义设备控制指令 - 停止设备
        /// </summary>
        public async Task<CommandResult> StopDeviceAsync(string deviceId, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("停止设备，设备ID: {DeviceId}", deviceId);
            return await SendCommandAsync(deviceId, "stop", true, cancellationToken);
        }

        /// <summary>
        /// 批量指令发送
        /// </summary>
        public async Task<BatchCommandResult> SendBatchCommandsAsync(string deviceId, 
            IEnumerable<CommandRequest> commands, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new BatchCommandResult();
            var commandList = commands.ToList();
            
            result.TotalCount = commandList.Count;
            
            try
            {
                _logger.LogInformation("批量发送指令，设备ID: {DeviceId}, 指令数量: {Count}", deviceId, result.TotalCount);

                foreach (var command in commandList)
                {
                    try
                    {
                        CommandResult commandResult;
                        
                        if (!string.IsNullOrEmpty(command.ProtocolItemName))
                        {
                            // 使用协议项名称发送
                            var typedResult = await SendByProtocolItemAsync<object>(deviceId, command.ProtocolItemName, 
                                command.Parameters, cancellationToken);
                            commandResult = new CommandResult
                            {
                                IsSuccess = typedResult.IsSuccess,
                                Message = typedResult.Message,
                                ErrorCode = typedResult.ErrorCode,
                                ExecutionDuration = typedResult.ExecutionDuration,
                                ExecutedAt = typedResult.ExecutedAt,
                                Metadata = typedResult.Metadata
                            };
                            
                            if (typedResult.Data != null)
                            {
                                commandResult.Metadata.Add("结果数据", typedResult.Data);
                            }
                        }
                        else
                        {
                            // 使用指令名称发送
                            commandResult = await SendCommandAsync(deviceId, command.CommandName, command.Parameters, cancellationToken);
                        }
                        
                        result.Results.Add(commandResult);
                        
                        if (commandResult.IsSuccess)
                        {
                            result.SuccessCount++;
                        }
                        else
                        {
                            result.FailedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorResult = new CommandResult
                        {
                            IsSuccess = false,
                            Message = $"指令执行异常: {ex.Message}",
                            ErrorCode = "BATCH_COMMAND_EXCEPTION",
                            ExecutedAt = DateTime.UtcNow
                        };
                        
                        result.Results.Add(errorResult);
                        result.FailedCount++;
                        
                        _logger.LogError(ex, "批量指令中的单个指令执行失败，指令: {CommandName}", command.CommandName);
                    }
                }
                
                _logger.LogInformation("批量指令执行完成，设备ID: {DeviceId}, 成功: {Success}, 失败: {Failed}", 
                    deviceId, result.SuccessCount, result.FailedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量指令执行异常，设备ID: {DeviceId}", deviceId);
                
                // 如果还没有任何结果，添加一个错误结果
                if (!result.Results.Any())
                {
                    result.Results.Add(new CommandResult
                    {
                        IsSuccess = false,
                        Message = $"批量指令执行异常: {ex.Message}",
                        ErrorCode = "BATCH_EXECUTION_EXCEPTION",
                        ExecutedAt = DateTime.UtcNow
                    });
                    result.FailedCount = result.TotalCount;
                }
            }
            finally
            {
                stopwatch.Stop();
                result.TotalExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 读取设备数据
        /// </summary>
        public async Task<CommandResult<T>> ReadDeviceDataAsync<T>(string deviceId, string protocolItemName, 
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("读取设备数据，设备ID: {DeviceId}, 协议项: {ProtocolItemName}", deviceId, protocolItemName);
            return await SendByProtocolItemAsync<T>(deviceId, protocolItemName, default(T), cancellationToken);
        }
    }
} 