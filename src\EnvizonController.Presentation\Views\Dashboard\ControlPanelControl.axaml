<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.ControlPanelControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:EnvizonController.Presentation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helpers="clr-namespace:EnvizonController.Presentation.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="100"
    d:DesignWidth="800"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <UserControl.Styles>
        <Style Selector="Button.glow2.Red">

            <Setter Property="Foreground" Value="#FF003C" />

        </Style>
        <Style Selector="Button.glow2.Red:pointerover">
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="30"
                    OffsetX="0.5"
                    OffsetY="0.5"
                    Opacity="1"
                    Color="#FF003C" />
            </Setter>
            <Setter Property="Background" Value="#FF003C" />
            <Setter Property="Foreground" Value="#0B1C2C" />

        </Style>

        <Style Selector="Button.glow2.Warning">

            <Setter Property="Foreground" Value="#FF9E00" />

        </Style>
        <Style Selector="Button.glow2.Warning:pointerover">
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="30"
                    OffsetX="0.5"
                    OffsetY="0.5"
                    Opacity="1"
                    Color="#FF9E00" />
            </Setter>
            <Setter Property="Background" Value="#FF9E00" />
            <Setter Property="Foreground" Value="#0B1C2C" />

        </Style>

        <Style Selector="Button.glow2.Success">

            <Setter Property="Foreground" Value="Lime" />

        </Style>
        <Style Selector="Button.glow2.Success:pointerover">
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="30"
                    OffsetX="0.5"
                    OffsetY="0.5"
                    Opacity="1"
                    Color="Lime" />
            </Setter>
            <Setter Property="Background" Value="Lime" />
            <Setter Property="Foreground" Value="#0B1C2C" />

        </Style>
    </UserControl.Styles>
    <UserControl.Resources>
        <converters:DeviceStatusToButtonVisibilityConverter x:Key="DeviceStatusToButtonVisibilityConverter" />
    </UserControl.Resources>
    <Border
        Padding="10,5"
        Background="#600B0A1A"
        Classes="cyber-border"
        CornerRadius="5">
        <Border.Transitions>
            <Transitions>
                <DoubleTransition
                    Easing="CubicEaseOut"
                    Property="Height"
                    Duration="0:0:0.3" />
                <DoubleTransition
                    Easing="CubicEaseOut"
                    Property="Opacity"
                    Duration="0:0:0.3" />
            </Transitions>
        </Border.Transitions>
        <Grid ColumnDefinitions="Auto,*">
            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                <Button
                    Margin="5,0"
                    helpers:CommonHelper.CommonBrush="Lime"
                    Classes="glow2 Success"
                    Command="{Binding StartTestCommand}"
                    IsVisible="{Binding CurrentDevice.DeviceDTO.OperatingStatus, Converter={StaticResource DeviceStatusToButtonVisibilityConverter}, ConverterParameter=Start}">

                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            FontSize="14"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="&#xf04b;" />
                        <TextBlock
                            Margin="5,0,0,0"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="启动" />
                    </StackPanel>
                </Button>

                <Button
                    Margin="5,0"
                    helpers:CommonHelper.CommonBrush="#FF9E00"
                    Classes="glow2 Warning"
                    Command="{Binding PauseTestCommand}"
                    IsVisible="{Binding CurrentDevice.DeviceDTO.OperatingStatus, Converter={StaticResource DeviceStatusToButtonVisibilityConverter}, ConverterParameter=Pause}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            FontSize="14"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="&#xf04c;" />
                        <TextBlock
                            Margin="5,0,0,0"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="暂停" />
                    </StackPanel>
                </Button>

                <Button
                    Margin="5,0"
                    helpers:CommonHelper.CommonBrush="#FF003C"
                    Classes="glow2 Red"
                    Command="{Binding StopTestCommand}"
                    IsVisible="{Binding CurrentDevice.DeviceDTO.OperatingStatus, Converter={StaticResource DeviceStatusToButtonVisibilityConverter}, ConverterParameter=Stop}">

                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            FontSize="14"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="&#xf04d;" />
                        <TextBlock
                            Margin="5,0,0,0"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="停止" />
                    </StackPanel>
                </Button>
            </StackPanel>


            <StackPanel
                Grid.Column="1"
                HorizontalAlignment="Right"
                Orientation="Horizontal">

                <Button Margin="5,0" Classes="glow2">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            FontSize="14"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="&#xf013;" />
                        <TextBlock
                            Margin="5,0,0,0"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="设置" />
                    </StackPanel>
                </Button>

                <Button Margin="5,0" Classes="glow2">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            FontSize="14"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="&#xf56e;" />
                        <TextBlock
                            Margin="5,0,0,0"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="导出" />
                    </StackPanel>
                </Button>

                <Button Margin="5,0" Classes="glow2">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            FontSize="14"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="&#xf0db;" />
                        <TextBlock
                            Margin="5,0,0,0"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="面板" />
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
