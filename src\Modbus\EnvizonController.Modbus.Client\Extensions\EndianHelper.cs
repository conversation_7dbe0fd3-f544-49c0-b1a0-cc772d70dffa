using System;

namespace EnvizonController.Modbus.Client.Extensions
{
    /// <summary>
    /// 大小端转换辅助类
    /// </summary>
    public static class EndianHelper
    {
        /// <summary>
        /// 字节序类型
        /// </summary>
        public enum ByteOrder
        {
            /// <summary>
            /// 大端序（高字节在前）
            /// </summary>
            BigEndian,
            
            /// <summary>
            /// 小端序（低字节在前）
            /// </summary>
            LittleEndian
        }

        /// <summary>
        /// 字序类型（适用于32位及以上数据类型）
        /// </summary>
        public enum WordOrder
        {
            /// <summary>
            /// 大端序（高字在前）
            /// </summary>
            BigEndian,
            
            /// <summary>
            /// 小端序（低字在前）
            /// </summary>
            LittleEndian
        }

        /// <summary>
        /// 将两个ushort合并为一个uint，考虑字序
        /// </summary>
        /// <param name="highWord">高位字</param>
        /// <param name="lowWord">低位字</param>
        /// <param name="wordOrder">字序</param>
        /// <returns>合并后的uint值</returns>
        public static uint CombineToUInt32(ushort highWord, ushort lowWord, WordOrder wordOrder)
        {
            return wordOrder == WordOrder.BigEndian
                ? ((uint)highWord << 16) | lowWord
                : ((uint)lowWord << 16) | highWord;
        }

        /// <summary>
        /// 将两个ushort合并为一个int，考虑字序
        /// </summary>
        /// <param name="highWord">高位字</param>
        /// <param name="lowWord">低位字</param>
        /// <param name="wordOrder">字序</param>
        /// <returns>合并后的int值</returns>
        public static int CombineToInt32(ushort highWord, ushort lowWord, WordOrder wordOrder)
        {
            return (int)CombineToUInt32(highWord, lowWord, wordOrder);
        }

        /// <summary>
        /// 将两个ushort合并为一个float，考虑字序
        /// </summary>
        /// <param name="highWord">高位字</param>
        /// <param name="lowWord">低位字</param>
        /// <param name="wordOrder">字序</param>
        /// <returns>合并后的float值</returns>
        public static float CombineToFloat(ushort highWord, ushort lowWord, WordOrder wordOrder)
        {
            uint value = CombineToUInt32(highWord, lowWord, wordOrder);
            return BitConverter.UInt32BitsToSingle(value);
        }

        /// <summary>
        /// 将四个ushort合并为一个double，考虑字序
        /// </summary>
        /// <param name="registers">寄存器数组（需要4个元素）</param>
        /// <param name="wordOrder">字序</param>
        /// <returns>合并后的double值</returns>
        public static double CombineToDouble(ushort[] registers, WordOrder wordOrder)
        {
            if (registers.Length < 4)
                throw new ArgumentException("需要至少4个寄存器来转换为double类型", nameof(registers));

            ulong value;
            if (wordOrder == WordOrder.BigEndian)
            {
                // 大端序：高字在前
                value = ((ulong)registers[0] << 48) |
                        ((ulong)registers[1] << 32) |
                        ((ulong)registers[2] << 16) |
                        registers[3];
            }
            else
            {
                // 小端序：低字在前
                value = ((ulong)registers[3] << 48) |
                        ((ulong)registers[2] << 32) |
                        ((ulong)registers[1] << 16) |
                        registers[0];
            }

            return BitConverter.UInt64BitsToDouble(value);
        }

        /// <summary>
        /// 将uint拆分为两个ushort，考虑字序
        /// </summary>
        /// <param name="value">要拆分的uint值</param>
        /// <param name="wordOrder">字序</param>
        /// <returns>拆分后的ushort数组</returns>
        public static ushort[] SplitUInt32(uint value, WordOrder wordOrder)
        {
            ushort highWord = (ushort)(value >> 16);
            ushort lowWord = (ushort)(value & 0xFFFF);

            return wordOrder == WordOrder.BigEndian
                ? new[] { highWord, lowWord }
                : new[] { lowWord, highWord };
        }

        /// <summary>
        /// 将int拆分为两个ushort，考虑字序
        /// </summary>
        /// <param name="value">要拆分的int值</param>
        /// <param name="wordOrder">字序</param>
        /// <returns>拆分后的ushort数组</returns>
        public static ushort[] SplitInt32(int value, WordOrder wordOrder)
        {
            return SplitUInt32((uint)value, wordOrder);
        }

        /// <summary>
        /// 将float拆分为两个ushort，考虑字序
        /// </summary>
        /// <param name="value">要拆分的float值</param>
        /// <param name="wordOrder">字序</param>
        /// <returns>拆分后的ushort数组</returns>
        public static ushort[] SplitFloat(float value, WordOrder wordOrder)
        {
            uint intValue = BitConverter.SingleToUInt32Bits(value);
            return SplitUInt32(intValue, wordOrder);
        }

        /// <summary>
        /// 将double拆分为四个ushort，考虑字序
        /// </summary>
        /// <param name="value">要拆分的double值</param>
        /// <param name="wordOrder">字序</param>
        /// <returns>拆分后的ushort数组</returns>
        public static ushort[] SplitDouble(double value, WordOrder wordOrder)
        {
            ulong longValue = BitConverter.DoubleToUInt64Bits(value);
            ushort[] result = new ushort[4];

            if (wordOrder == WordOrder.BigEndian)
            {
                // 大端序：高字在前
                result[0] = (ushort)(longValue >> 48);
                result[1] = (ushort)(longValue >> 32);
                result[2] = (ushort)(longValue >> 16);
                result[3] = (ushort)(longValue & 0xFFFF);
            }
            else
            {
                // 小端序：低字在前
                result[0] = (ushort)(longValue & 0xFFFF);
                result[1] = (ushort)(longValue >> 16);
                result[2] = (ushort)(longValue >> 32);
                result[3] = (ushort)(longValue >> 48);
            }

            return result;
        }

        /// <summary>
        /// 将字符串转换为ushort数组
        /// </summary>
        /// <param name="value">要转换的字符串</param>
        /// <param name="byteOrder">字节序</param>
        /// <returns>转换后的ushort数组</returns>
        public static ushort[] StringToRegisters(string value, ByteOrder byteOrder)
        {
            if (string.IsNullOrEmpty(value))
                return Array.Empty<ushort>();

            // 计算需要的寄存器数量（每个寄存器2个字节）
            int registerCount = (value.Length + 1) / 2; // +1 是为了处理奇数长度的字符串
            ushort[] registers = new ushort[registerCount];

            for (int i = 0; i < registerCount; i++)
            {
                // 计算当前寄存器对应的字符索引
                int charIndex = i * 2;
                
                // 获取第一个字符（如果存在）
                char char1 = charIndex < value.Length ? value[charIndex] : '\0';
                
                // 获取第二个字符（如果存在）
                char char2 = charIndex + 1 < value.Length ? value[charIndex + 1] : '\0';

                // 根据字节序组合字符
                registers[i] = byteOrder == ByteOrder.BigEndian
                    ? (ushort)((char1 << 8) | char2)
                    : (ushort)((char2 << 8) | char1);
            }

            return registers;
        }

        /// <summary>
        /// 将ushort数组转换为字符串
        /// </summary>
        /// <param name="registers">寄存器数组</param>
        /// <param name="byteOrder">字节序</param>
        /// <returns>转换后的字符串</returns>
        public static string RegistersToString(ushort[] registers, ByteOrder byteOrder)
        {
            if (registers == null || registers.Length == 0)
                return string.Empty;

            // 每个寄存器可以存储2个字符
            char[] chars = new char[registers.Length * 2];

            for (int i = 0; i < registers.Length; i++)
            {
                ushort register = registers[i];
                
                // 根据字节序提取字符
                char char1, char2;
                if (byteOrder == ByteOrder.BigEndian)
                {
                    char1 = (char)(register >> 8);
                    char2 = (char)(register & 0xFF);
                }
                else
                {
                    char1 = (char)(register & 0xFF);
                    char2 = (char)(register >> 8);
                }

                // 存储字符
                chars[i * 2] = char1;
                chars[i * 2 + 1] = char2;
            }

            // 移除尾部的空字符
            return new string(chars).TrimEnd('\0');
        }
    }
}
