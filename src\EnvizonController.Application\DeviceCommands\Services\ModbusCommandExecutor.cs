using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Application.DeviceCommands.Interfaces;
using EnvizonController.Application.DeviceCommands.Models;
using EnvizonController.Shared.Enums;
using EnvizonController.Modbus.Client.Extensions;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace EnvizonController.Application.DeviceCommands.Services
{
    /// <summary>
    /// Modbus指令执行器实现
    /// </summary>
    public class ModbusCommandExecutor : ICommandExecutor
    {
        private readonly ILogger<ModbusCommandExecutor> _logger;

        public ModbusCommandExecutor(ILogger<ModbusCommandExecutor> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 执行读取指令
        /// </summary>
        public async Task<CommandResult<T>> ExecuteReadCommandAsync<T>(IModbusClient client, ProtocolItem protocolItem, 
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CommandResult<T>();

            try
            {
                _logger.LogDebug("执行读取指令，协议项: {ItemName}, 地址: {Address}, 类型: {DataType}", 
                    protocolItem.DisplayName, protocolItem.Address, protocolItem.DataType);

                if (!IsCommandTypeSupported(protocolItem, CommandType.Read))
                {
                    result.IsSuccess = false;
                    result.Message = $"协议项 '{protocolItem.DisplayName}' 不支持读取操作";
                    result.ErrorCode = "UNSUPPORTED_OPERATION";
                    return result;
                }

                object rawValue = await ReadProtocolItemValueAsync(client, protocolItem, cancellationToken);
                
                // 应用缩放因子和偏移量
                if (rawValue is double doubleValue)
                {
                    doubleValue = doubleValue * protocolItem.ScaleFactor + protocolItem.Offset;
                    rawValue = doubleValue;
                }

                T convertedValue = ConvertValue<T>(rawValue, protocolItem);

                result.IsSuccess = true;
                result.Data = convertedValue;
                result.Message = "读取成功";
                result.Metadata.Add("原始值", rawValue);
                result.Metadata.Add("单位", protocolItem.Unit);
                result.Metadata.Add("地址", protocolItem.Address);

                _logger.LogDebug("读取成功，协议项: {ItemName}, 值: {Value}", protocolItem.DisplayName, convertedValue);
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"读取失败: {ex.Message}";
                result.ErrorCode = "READ_ERROR";
                _logger.LogError(ex, "读取协议项失败，协议项: {ItemName}", protocolItem.DisplayName);
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionDuration = stopwatch.Elapsed;
                result.ExecutedAt = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// 执行写入指令
        /// </summary>
        public async Task<CommandResult> ExecuteWriteCommandAsync<T>(IModbusClient client, ProtocolItem protocolItem, 
            T value, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CommandResult();

            try
            {
                _logger.LogDebug("执行写入指令，协议项: {ItemName}, 地址: {Address}, 值: {Value}", 
                    protocolItem.DisplayName, protocolItem.Address, value);

                if (!IsCommandTypeSupported(protocolItem, CommandType.Write))
                {
                    result.IsSuccess = false;
                    result.Message = $"协议项 '{protocolItem.DisplayName}' 不支持写入操作";
                    result.ErrorCode = "UNSUPPORTED_OPERATION";
                    return result;
                }

                // 应用逆向缩放因子和偏移量
                object writeValue = value!;
                if (writeValue is double doubleValue && protocolItem.ScaleFactor != 0)
                {
                    doubleValue = (doubleValue - protocolItem.Offset) / protocolItem.ScaleFactor;
                    writeValue = doubleValue;
                }

                bool success = await WriteProtocolItemValueAsync(client, protocolItem, writeValue, cancellationToken);

                if (success)
                {
                    result.IsSuccess = true;
                    result.Message = "写入成功";
                    result.Metadata.Add("写入值", value!);
                    result.Metadata.Add("原始写入值", writeValue);
                    result.Metadata.Add("地址", protocolItem.Address);

                    _logger.LogDebug("写入成功，协议项: {ItemName}, 值: {Value}", protocolItem.DisplayName, value);
                }
                else
                {
                    result.IsSuccess = false;
                    result.Message = "写入操作返回失败";
                    result.ErrorCode = "WRITE_FAILED";
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"写入失败: {ex.Message}";
                result.ErrorCode = "WRITE_ERROR";
                _logger.LogError(ex, "写入协议项失败，协议项: {ItemName}, 值: {Value}", protocolItem.DisplayName, value);
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionDuration = stopwatch.Elapsed;
                result.ExecutedAt = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// 执行自定义指令
        /// </summary>
        public async Task<CommandResult> ExecuteCustomCommandAsync(IModbusClient client, CustomCommand command, 
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CommandResult();

            try
            {
                _logger.LogDebug("执行自定义指令，名称: {CommandName}, 功能码: {FunctionCode}, 地址: {Address}", 
                    command.Name, command.FunctionCode, command.Address);

                // 这里可以根据功能码执行相应的Modbus操作
                // 暂时返回成功状态，具体实现根据需求来定
                result.IsSuccess = true;
                result.Message = "自定义指令执行成功";
                result.Metadata.Add("指令名称", command.Name);
                result.Metadata.Add("功能码", command.FunctionCode);
                result.Metadata.Add("地址", command.Address);

                await Task.Delay(100, cancellationToken); // 模拟执行时间
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"自定义指令执行失败: {ex.Message}";
                result.ErrorCode = "CUSTOM_COMMAND_ERROR";
                _logger.LogError(ex, "自定义指令执行失败，指令: {CommandName}", command.Name);
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionDuration = stopwatch.Elapsed;
                result.ExecutedAt = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// 验证并转换数据类型
        /// </summary>
        public T ConvertValue<T>(object value, ProtocolItem protocolItem)
        {
            try
            {
                if (value == null)
                    return default(T)!;

                Type targetType = typeof(T);
                Type? nullableType = Nullable.GetUnderlyingType(targetType);
                if (nullableType != null)
                    targetType = nullableType;

                // 如果类型已经匹配，直接返回
                if (value.GetType() == targetType || targetType.IsAssignableFrom(value.GetType()))
                    return (T)value;

                // 尝试类型转换
                if (targetType == typeof(string))
                    return (T)(object)value.ToString()!;

                if (targetType == typeof(bool))
                {
                    if (value is double doubleVal)
                        return (T)(object)(doubleVal != 0);
                    if (value is int intVal)
                        return (T)(object)(intVal != 0);
                    return (T)Convert.ChangeType(value, targetType);
                }

                return (T)Convert.ChangeType(value, targetType);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "类型转换失败，值: {Value}, 目标类型: {TargetType}", value, typeof(T));
                return default(T)!;
            }
        }

        /// <summary>
        /// 检查协议项是否支持指定操作
        /// </summary>
        public bool IsCommandTypeSupported(ProtocolItem protocolItem, CommandType commandType)
        {
            return commandType switch
            {
                CommandType.Read => true, // 所有协议项都支持读取
                CommandType.Write => protocolItem.IsWritable,
                CommandType.Custom => true, // 自定义指令暂时都支持
                _ => false
            };
        }

        /// <summary>
        /// 读取协议项值（根据数据类型）
        /// </summary>
        private async Task<object> ReadProtocolItemValueAsync(IModbusClient client, ProtocolItem protocolItem, 
            CancellationToken cancellationToken)
        {
            byte slaveId = protocolItem.UnitId;
            ushort address = protocolItem.Address;

            return protocolItem.DataType switch
            {
                DataType.Float => await client.ReadFloatAsync(slaveId, address, 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Double => await client.ReadDoubleAsync(slaveId, address, 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Int16 => await client.ReadInt16Async(slaveId, address, cancellationToken),
                DataType.UInt16 => await client.ReadUInt16Async(slaveId, address, cancellationToken),
                DataType.Int32 => await client.ReadInt32Async(slaveId, address, 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.UInt32 => await client.ReadUInt32Async(slaveId, address, 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Bool => (await client.ReadCoilsAsync(slaveId, address, 1, cancellationToken))[0],
                DataType.String => await client.ReadStringAsync(slaveId, address, 
                    protocolItem.RegisterCount, EndianHelper.ByteOrder.BigEndian, cancellationToken),
                _ => throw new NotSupportedException($"不支持的数据类型: {protocolItem.DataType}")
            };
        }

        /// <summary>
        /// 写入协议项值（根据数据类型）
        /// </summary>
        private async Task<bool> WriteProtocolItemValueAsync(IModbusClient client, ProtocolItem protocolItem, 
            object value, CancellationToken cancellationToken)
        {
            byte slaveId = protocolItem.UnitId;
            ushort address = protocolItem.Address;

            return protocolItem.DataType switch
            {
                DataType.Float => await client.WriteFloatAsync(slaveId, address, Convert.ToSingle(value), 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Double => await client.WriteDoubleAsync(slaveId, address, Convert.ToDouble(value), 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Int16 => await client.WriteInt16Async(slaveId, address, Convert.ToInt16(value), cancellationToken),
                DataType.UInt16 => await client.WriteUInt16Async(slaveId, address, Convert.ToUInt16(value), cancellationToken),
                DataType.Int32 => await client.WriteInt32Async(slaveId, address, Convert.ToInt32(value), 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.UInt32 => await client.WriteUInt32Async(slaveId, address, Convert.ToUInt32(value), 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Bool => await client.WriteSingleCoilAsync(slaveId, address, Convert.ToBoolean(value), cancellationToken),
                DataType.String => await client.WriteStringAsync(slaveId, address, value.ToString()!, 
                    EndianHelper.ByteOrder.BigEndian, cancellationToken),
                _ => throw new NotSupportedException($"不支持的数据类型: {protocolItem.DataType}")
            };
        }
    }
} 